using IntegrationTests.Infrastructure;
using Microsoft.Extensions.Logging;
using Raven.Client.Documents.Session;
using Shouldly;
using ToroEhr.Domain;
using ToroEhr.Enums;
using ToroEhr.Services;
using Xunit;

namespace IntegrationTests.Services;

[Trait("MissedAppointmentSchedulerService", "Unit Tests")]
public class MissedAppointmentSchedulerServiceTests : IClassFixture<MissedAppointmentSchedulerServiceTestsFixture>
{
    private readonly MissedAppointmentSchedulerServiceTestsFixture _fixture;

    public MissedAppointmentSchedulerServiceTests(MissedAppointmentSchedulerServiceTestsFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact(DisplayName = "1. Should mark appointment and encounter as missed when past MarkMissedTime")]
    public async Task ShouldMarkAppointmentAsMissed()
    {
        using IAsyncDocumentSession session = _fixture.DocumentStore.OpenAsyncSession();

        // verify appointment and encounter are marked as missed
        var appointment = await session.LoadAsync<ToroEhr.Domain.Appointment>(_fixture.MissedAppointmentId);
        var encounter = await session.LoadAsync<ToroEhr.Domain.Encounter>(_fixture.MissedEncounterId);

        appointment.ShouldNotBeNull();
        encounter.ShouldNotBeNull();
        
        appointment.Status.ShouldBe(AppointmentStatus.Missed.Name);
        encounter.Status.ShouldBe(EncounterStatus.Missed.Name);
    }

    [Fact(DisplayName = "2. Should not mark appointment as missed when within MarkMissedTime")]
    public async Task ShouldNotMarkRecentAppointmentAsMissed()
    {
        using IAsyncDocumentSession session = _fixture.DocumentStore.OpenAsyncSession();

        // verify appointment and encounter are still confirmed/planned
        var appointment = await session.LoadAsync<ToroEhr.Domain.Appointment>(_fixture.RecentAppointmentId);
        var encounter = await session.LoadAsync<ToroEhr.Domain.Encounter>(_fixture.RecentEncounterId);

        appointment.ShouldNotBeNull();
        encounter.ShouldNotBeNull();
        
        appointment.Status.ShouldBe(AppointmentStatus.Confirmed.Name);
        encounter.Status.ShouldBe(EncounterStatus.Planned.Name);
    }

    [Fact(DisplayName = "3. Should not process already missed appointments")]
    public async Task ShouldNotProcessAlreadyMissedAppointments()
    {
        using IAsyncDocumentSession session = _fixture.DocumentStore.OpenAsyncSession();

        // verify already missed appointment remains missed
        var appointment = await session.LoadAsync<ToroEhr.Domain.Appointment>(_fixture.AlreadyMissedAppointmentId);
        var encounter = await session.LoadAsync<ToroEhr.Domain.Encounter>(_fixture.AlreadyMissedEncounterId);

        appointment.ShouldNotBeNull();
        encounter.ShouldNotBeNull();
        
        appointment.Status.ShouldBe(AppointmentStatus.Missed.Name);
        encounter.Status.ShouldBe(EncounterStatus.Missed.Name);
    }
}

public class MissedAppointmentSchedulerServiceTestsFixture : BaseFixture
{
    public string MissedAppointmentId { get; private set; } = null!;
    public string MissedEncounterId { get; private set; } = null!;
    public string RecentAppointmentId { get; private set; } = null!;
    public string RecentEncounterId { get; private set; } = null!;
    public string AlreadyMissedAppointmentId { get; private set; } = null!;
    public string AlreadyMissedEncounterId { get; private set; } = null!;

    public override async Task InitializeAsync()
    {
        await base.InitializeAsync();
        await CreateEmployeeDoctorUserWithSession(Session);

        var currentTime = DateTimeOffset.UtcNow;
        
        // create appointment that should be marked as missed (started 3 hours ago, MarkMissedTime is 2 hours)
        var missedEncounter = ToroEhr.Domain.Encounter.Create(Patient.Id, EmployeeDoctor.Id, Location.Id, Location.Classification, currentTime.AddHours(-3));
        await Session.StoreAsync(missedEncounter);
        MissedEncounterId = missedEncounter.Id;

        var missedAppointment = ToroEhr.Domain.Appointment.Create(Patient.Id, EmployeeDoctor.Id, Location.Id, missedEncounter.Id,
            currentTime.AddHours(-3), currentTime.AddHours(-3).AddMinutes(30), 30, AppointmentStatus.Confirmed);
        await Session.StoreAsync(missedAppointment);
        MissedAppointmentId = missedAppointment.Id;

        // create recent appointment that should NOT be marked as missed (started 1 hour ago, MarkMissedTime is 2 hours)
        var recentEncounter = ToroEhr.Domain.Encounter.Create(Patient.Id, EmployeeDoctor.Id, Location.Id, Location.Classification, currentTime.AddHours(-1));
        await Session.StoreAsync(recentEncounter);
        RecentEncounterId = recentEncounter.Id;

        var recentAppointment = ToroEhr.Domain.Appointment.Create(Patient.Id, EmployeeDoctor.Id, Location.Id, recentEncounter.Id,
            currentTime.AddHours(-1), currentTime.AddHours(-1).AddMinutes(30), 30, AppointmentStatus.Confirmed);
        await Session.StoreAsync(recentAppointment);
        RecentAppointmentId = recentAppointment.Id;

        // create already missed appointment to ensure it's not processed again
        var alreadyMissedEncounter = ToroEhr.Domain.Encounter.Create(Patient.Id, EmployeeDoctor.Id, Location.Id, Location.Classification, currentTime.AddHours(-5));
        alreadyMissedEncounter.MarkAsMissed();
        await Session.StoreAsync(alreadyMissedEncounter);
        AlreadyMissedEncounterId = alreadyMissedEncounter.Id;

        var alreadyMissedAppointment = ToroEhr.Domain.Appointment.Create(Patient.Id, EmployeeDoctor.Id, Location.Id, alreadyMissedEncounter.Id,
            currentTime.AddHours(-5), currentTime.AddHours(-5).AddMinutes(30), 30, AppointmentStatus.Missed);
        await Session.StoreAsync(alreadyMissedAppointment);
        AlreadyMissedAppointmentId = alreadyMissedAppointment.Id;

        await Session.SaveChangesAsync();

        // run the scheduler service
        var logger = new LoggerFactory().CreateLogger<MissedAppointmentSchedulerService>();
        var schedulerService = new MissedAppointmentSchedulerService(DocumentStore, logger);
        await schedulerService.Invoke();
    }
}
