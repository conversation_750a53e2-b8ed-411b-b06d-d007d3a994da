using Coravel.Invocable;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using ToroEhr.Domain;
using ToroEhr.Enums;

namespace ToroEhr.Services;

public class MissedAppointmentSchedulerService : IInvocable, ICancellableInvocable
{
    private readonly IDocumentStore _store;
    private readonly ILogger<MissedAppointmentSchedulerService> _logger;

    public CancellationToken CancellationToken { get; set; }

    public MissedAppointmentSchedulerService(IDocumentStore store, ILogger<MissedAppointmentSchedulerService> logger)
    {
        _store = store;
        _logger = logger;
    }

    public async Task Invoke()
    {
        if (CancellationToken.IsCancellationRequested) return;
        await ProcessMissedAppointments(CancellationToken);
    }

    private async Task ProcessMissedAppointments(CancellationToken cancellationToken)
    {
        try
        {
            using IAsyncDocumentSession session = _store.OpenAsyncSession();
            
            var currentTime = DateTimeOffset.UtcNow;
            
            // get appointments that could potentially be missed
            var appointments = await session.Query<Appointment>()
                .Include(x => x.LocationId)
                .Include(x => x.EncounterId)
                .Where(x => x.Status == AppointmentStatus.Confirmed.Name || 
                           x.Status == AppointmentStatus.CheckedIn.Name)
                .Where(x => x.StartAt < currentTime) // only past appointments
                .ToListAsync(cancellationToken);

            var missedCount = 0;

            foreach (var appointment in appointments)
            {
                if (cancellationToken.IsCancellationRequested) break;

                var location = await session.LoadAsync<Location>(appointment.LocationId, cancellationToken);
                if (location == null) continue;

                // calculate if appointment should be marked as missed
                var missedThreshold = appointment.StartAt.AddHours(location.MarkMissedTime);
                
                if (currentTime >= missedThreshold)
                {
                    // mark appointment as missed
                    appointment.MarkAsMissed();
                    
                    // mark encounter as missed if it exists
                    if (!string.IsNullOrEmpty(appointment.EncounterId))
                    {
                        var encounter = await session.LoadAsync<Encounter>(appointment.EncounterId, cancellationToken);
                        if (encounter != null)
                        {
                            encounter.MarkAsMissed();
                        }
                    }
                    
                    missedCount++;
                }
            }

            if (missedCount > 0)
            {
                await session.SaveChangesAsync(cancellationToken);
                _logger.LogInformation("Marked {MissedCount} appointments as missed", missedCount);
            }
            else
            {
                _logger.LogDebug("No appointments to mark as missed");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing missed appointments");
            throw;
        }
    }
}
